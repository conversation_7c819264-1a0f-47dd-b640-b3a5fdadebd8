# Schema Generation Patterns with Drizzle-Zod

This document explains how we use `drizzle-zod` to automatically generate Zod validation schemas from our Drizzle ORM database schemas, eliminating duplication and ensuring consistency.

## Overview

Instead of manually defining Zod schemas for API validation that duplicate our database schema definitions, we use `drizzle-zod` to auto-generate them. This provides:

- **DRY Principle**: Single source of truth for data structure
- **Type Safety**: Automatic TypeScript inference 
- **Consistency**: API schemas always match database schemas
- **Maintainability**: Schema changes propagate automatically
- **Reduced Errors**: No manual synchronization required

## Setup

We have `drizzle-zod` installed as a dependency:

```json
{
  "dependencies": {
    "drizzle-zod": "^0.5.1"
  }
}
```

## Core Pattern

### 1. Base Schema Generation

In `src/lib/schema-generators.ts`, we auto-generate base schemas:

```typescript
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { listings, listingDetails, userProfiles } from "@/db/schema";

// Auto-generated insert schema (for creating records)
export const insertListingSchema = createInsertSchema(listings, {
  // Override specific fields with custom validation
  askingPrice: z.coerce.number().positive().optional(),
  yearEstablished: z.coerce.number().int().min(1800).max(new Date().getFullYear()).optional(),
  // Transform date strings
  dateListed: z.string().date().optional(),
});

// Auto-generated select schema (for reading records)
export const selectListingSchema = createSelectSchema(listings, {
  // Override numeric fields to ensure they come back as numbers
  askingPrice: z.number().optional(),
  cashFlowSde: z.number().optional(),
});
```

### 2. API Schema Enhancement

Build API-specific schemas from the base schemas:

```typescript
// Create request schema - omit server-managed fields
export const createListingRequestSchema = insertListingSchema
  .omit({ 
    id: true, 
    createdAt: true, 
    updatedAt: true,
    workspaceId: true,
    createdBy: true,
  })
  .extend({
    // Add nested objects for complex data
    details: z.object({
      business_description: z.string().optional(),
      financial_details: z.object({
        revenue_2023: z.number().optional(),
        ebitda: z.number().optional(),
      }).optional(),
    }).optional(),
  })
  .openapi("CreateListingRequest");

// Update schema is just a partial of create
export const updateListingRequestSchema = createListingRequestSchema
  .partial()
  .openapi("UpdateListingRequest");

// Response schema includes computed/relation fields  
export const listingResponseSchema = selectListingSchema
  .extend({
    details: selectListingDetailsSchema.optional(),
    days_listed: z.number().int().optional(),
  })
  .openapi("Listing");
```

### 3. Response Wrappers

Create consistent API response formats:

```typescript
export const singleListingResponseSchema = z.object({
  success: z.boolean(),
  data: listingResponseSchema,
}).openapi("ListingResponse");

export const listingListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(listingResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
}).openapi("ListingListResponse");
```

## Usage in Route Files

Import and use the generated schemas:

```typescript
// Before (manual duplication)
const createListingRequestSchema = z.object({
  business_name: z.string().min(1, "Business name is required"),
  industry: z.string().min(1, "Industry is required"),
  asking_price: z.number().positive().optional(),
  // ... 50+ more fields manually defined
}).openapi("CreateListingRequest");

// After (generated from database schema)
import { 
  createListingRequestSchema, 
  singleListingResponseSchema 
} from "@/lib/schema-generators";

export const createListingRoute = createRoute({
  method: "post",
  path: "/v1/listings",
  request: {
    body: {
      content: {
        "application/json": {
          schema: createListingRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: singleListingResponseSchema,
        },
      },
      description: "Listing created successfully",
    },
  },
});
```

## Advanced Patterns

### Custom Field Validation

Override specific fields with enhanced validation:

```typescript
export const insertUserProfileSchema = createInsertSchema(userProfiles, {
  email: z.string().email("Invalid email format"),
  role: z.enum(['owner', 'admin', 'member', 'viewer']).optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone format").optional(),
});
```

### Special-Purpose Schemas

Some schemas need different approaches:

```typescript
// CSV imports - all fields are strings that need parsing
export const csvBulkImportRequestSchema = z.object({
  business_name: z.string().min(1, "Business name is required"),
  asking_price: z.string().optional(), // Will be parsed to number later
  year_established: z.string().optional(), // Will be parsed to number later
}).openapi("CsvBulkImportRequest");
```

### Relation Handling

For complex nested data:

```typescript
export const listingWithDetailsSchema = selectListingSchema.extend({
  details: selectListingDetailsSchema.optional(),
  status_history: z.array(selectListingStatusHistorySchema).optional(),
});
```

## Benefits Demonstrated

### Before: Manual Schema Definition (738 lines)
- Duplicate field definitions
- Manual synchronization required
- Type inconsistencies possible
- Maintenance overhead

### After: Generated Schemas (~200 lines)
- Single source of truth
- Automatic synchronization
- Type-safe by design
- Minimal maintenance

## Best Practices

### 1. Organize by Entity
Create separate sections for each database table:

```typescript
// Listing schemas
export const insertListingSchema = createInsertSchema(listings);
export const selectListingSchema = createSelectSchema(listings);

// User Profile schemas  
export const insertUserProfileSchema = createInsertSchema(userProfiles);
export const selectUserProfileSchema = createSelectSchema(userProfiles);
```

### 2. Layer Your Schemas
Build from base to specific:

```typescript
// Base schema (from database)
const baseSchema = createInsertSchema(table);

// API request schema (omit server fields)
const requestSchema = baseSchema.omit({...});

// API response schema (add computed fields)
const responseSchema = selectSchema.extend({...});
```

### 3. Use Consistent Naming
Follow clear naming conventions:

- `insert{Entity}Schema` - For database inserts
- `select{Entity}Schema` - For database selects  
- `create{Entity}RequestSchema` - For API create requests
- `update{Entity}RequestSchema` - For API update requests
- `{entity}ResponseSchema` - For API responses

### 4. Override Thoughtfully
Only override when you need:

- Enhanced validation (email format, regex patterns)
- Type coercion (string to number)
- Custom enums
- Default values

### 5. Document Special Cases
Add comments explaining deviations:

```typescript
// CSV schemas use strings because CSV parsing requires text input
export const csvImportSchema = z.object({
  price: z.string().optional(), // Parsed to number later
});
```

## Migration Guide

To migrate existing manual schemas:

1. **Identify the base table** the schema represents
2. **Generate base schemas** using `createInsertSchema`/`createSelectSchema`
3. **Add overrides** for custom validation only
4. **Build API schemas** by omitting/extending base schemas
5. **Update imports** in route files
6. **Remove duplicate** manual schema definitions
7. **Test thoroughly** to ensure no breaking changes

## When NOT to Use This Pattern

Keep manual schemas for:

- **Pure validation schemas** not tied to database tables
- **External API schemas** that don't match your database
- **Complex transformations** that can't be expressed with simple overrides
- **Legacy compatibility** where breaking changes aren't acceptable

## Performance Considerations

- Generated schemas are created at module load time
- No runtime performance impact
- Slightly larger bundle size (negligible)
- Faster development due to reduced manual work

## Conclusion

Using `drizzle-zod` for schema generation significantly reduces code duplication, ensures consistency between database and API schemas, and makes the codebase more maintainable. The pattern scales well and should be adopted for all new entities going forward. 