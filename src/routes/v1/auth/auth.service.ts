import { auth } from "@/lib/auth";
import db from "@/db";
import { userProfiles, organization, member, user } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";
import { randomUUID } from "crypto";

export interface SignUpData {
  name: string;
  email: string;
  password: string;
  companyName: string;
  companyType: "individual" | "team" | "firm";
  firstName: string;
  lastName: string;
  phone?: string;
  licenseNumber?: string;
  website?: string;
  businessAddress?: string;
  marketingConsent?: boolean;
  termsAccepted: boolean;
  image?: string;
  callbackURL?: string;
  rememberMe?: boolean;
}

export interface SignInData {
  email: string;
  password: string;
  callbackURL?: string;
  rememberMe?: boolean;
}

export interface OrganizationData {
  companyName: string;
  companyType: "individual" | "team" | "firm";
  firstName: string;
  lastName: string;
  phone?: string;
  licenseNumber?: string;
  website?: string;
  businessAddress?: string;
  marketingConsent?: boolean;
  termsAccepted: boolean;
}

export interface SessionData {
  id: string;
  userId: string;
  expiresAt: string;
  token: string;
  ipAddress: string | null;
  userAgent: string | null;
  activeOrganizationId: string | null;
  createdAt: string;
  updatedAt: string;
  impersonatedBy: string | null;
}

export interface AuthResponse {
  user: any;
  session: SessionData | null;
  organization: any;
  profile: any;
}

export class AuthService {

  /**
   * Check if a user with the given email exists in the database
   */
  static async checkEmailExists(email: string): Promise<boolean> {
    try {
      const existingUser = await db
        .select({ id: user.id })
        .from(user)
        .where(eq(user.email, email.toLowerCase()))
        .limit(1);

      return existingUser.length > 0;
    } catch (error) {
      console.error("Error checking email existence:", error);
      throw new HTTPException(500, { message: "Failed to check email existence" });
    }
  }

  /**
   * Sign in a user with email and password
   */
  static async signInEmail(data: SignInData): Promise<{ authResponse: AuthResponse; betterAuthResponse: Response }> {
    try {
      // Call Better Auth sign in API
      const response = await auth.api.signInEmail({
        body: {
          email: data.email,
          password: data.password,
          callbackURL: data.callbackURL,
          rememberMe: data.rememberMe,
        },
        asResponse: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new HTTPException(response.status as any, {
          message: errorData.message || "Sign in failed"
        });
      }

      const authData = await response.json();

      // Debug: Log what Better Auth returns
      console.log('Better Auth sign-in response:', JSON.stringify(authData, null, 2));

      // Create session object from Better Auth token response
      const sessionData: SessionData | null = authData.token ? {
        id: authData.token, // Using token as session ID for now
        userId: authData.user.id,
        token: authData.token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        createdAt: authData.user.createdAt,
        updatedAt: authData.user.updatedAt,
        ipAddress: null,
        userAgent: null,
        activeOrganizationId: null,
        impersonatedBy: null,
      } : null;

      // Get additional data
      const enrichedData = await this.getEnrichedAuthData(authData.user.id);

      const authResponse: AuthResponse = {
        user: authData.user,
        session: sessionData,
        organization: enrichedData.organization,
        profile: enrichedData.profile,
      };

      // Return both the auth data and the Better Auth response (for cookies)
      return {
        authResponse,
        betterAuthResponse: response
      };
    } catch (error: any) {
      if (error instanceof HTTPException) {
        throw error;
      }

      // Handle specific Better Auth errors
      if (error.message?.includes("Invalid credentials")) {
        throw new HTTPException(401, { message: "Invalid email or password" });
      }

      console.error("Sign in error:", error);
      throw new HTTPException(500, { message: "Internal server error during sign in" });
    }
  }

  /**
   * Sign out the current user
   */
  static async signOut(headers: Headers): Promise<{ betterAuthResponse: Response }> {
    try {
      // First check if there's a valid session
      const session = await auth.api.getSession({
        headers
      });

      // If no session exists, create a mock successful response
      if (!session?.session) {
        // Create a mock response that indicates successful sign-out
        const mockResponse = new Response(JSON.stringify({ success: true }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            // Add a cookie header to clear any potential session cookies
            'Set-Cookie': 'rendyr.session=; Path=/; HttpOnly; SameSite=Lax; Max-Age=0'
          }
        });

        return {
          betterAuthResponse: mockResponse
        };
      }

      // Call Better Auth sign out API if session exists
      const response = await auth.api.signOut({
        headers,
        asResponse: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new HTTPException(response.status as any, {
          message: errorData.message || "Sign out failed"
        });
      }

      // Return the Better Auth response (for cookies)
      return {
        betterAuthResponse: response
      };
    } catch (error: any) {
      if (error instanceof HTTPException) {
        throw error;
      }

      console.error("Sign out error:", error);
      throw new HTTPException(500, { message: "Internal server error during sign out" });
    }
  }

  /**
   * Get enriched auth data including organization and profile
   * Uses direct DB queries since session data is handled by Better Auth cookies
   */
  static async getEnrichedAuthData(userId: string) {
    try {
      let organizationRecord = null;
      let profileRecord = null;

      // Get user's organization using direct DB query since Better Auth API requires auth
      // This is reliable since the organization was just created by hooks
      try {
        const membershipData = await db
          .select({
            organization: organization,
            member: member,
          })
          .from(member)
          .innerJoin(organization, eq(member.organizationId, organization.id))
          .where(eq(member.userId, userId))
          .limit(1);

        organizationRecord = membershipData[0]?.organization || null;
      } catch (error) {
        console.log('Could not get organization from database:', error);
      }

      // Get user profile using direct DB query
      try {
        const profileData = await db
          .select()
          .from(userProfiles)
          .where(eq(userProfiles.userId, userId))
          .limit(1);

        profileRecord = profileData[0] || null;
      } catch (error) {
        console.log('Could not get user profile:', error);
      }

      return {
        organization: organizationRecord,
        profile: profileRecord,
      };
    } catch (error) {
      console.error("Error getting enriched auth data:", error);
      return {
        organization: null,
        profile: null,
      };
    }
  }

  /**
   * Create a new organization using Better Auth plugin
   */
  static async createOrganization(userId: string, orgData: OrganizationData): Promise<string> {
    try {
      // Validate required data
      if (!orgData.companyName) {
        throw new HTTPException(400, { message: "Company name is required for organization creation" });
      }

      // Generate organization name and slug
      const organizationName = orgData.companyName;
      const slug = organizationName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

      // Create metadata
      const metadata = {
        plan: "trial",
        status: "trial",
        companyType: orgData.companyType,
        phone: orgData.phone,
        licenseNumber: orgData.licenseNumber,
        website: orgData.website,
        businessAddress: orgData.businessAddress,
        marketingConsent: orgData.marketingConsent,
        termsAccepted: orgData.termsAccepted,
      };

      // Use Better Auth organization plugin
      const organizationData = await auth.api.createOrganization({
        body: {
          name: organizationName,
          slug: slug,
          logo: undefined,
          metadata,
          userId: userId, // server-only
          keepCurrentActiveOrganization: false,
        },
      });

      if (!organizationData) {
        throw new HTTPException(500, { message: "Failed to create organization - no data returned" });
      }

      console.log('Debug - Created organization with ID:', organizationData.id);

      // Verify and ensure the member role is set to "owner"
      // Better Auth should handle this automatically, but let's verify
      try {
        const membershipData = await db
          .select()
          .from(member)
          .where(and(eq(member.userId, userId), eq(member.organizationId, organizationData.id)))
          .limit(1);

        if (membershipData[0] && membershipData[0].role !== "owner") {
          // Update the role to "owner" if it's not already set correctly
          await db
            .update(member)
            .set({ role: "owner" })
            .where(and(eq(member.userId, userId), eq(member.organizationId, organizationData.id)));

          console.log('Debug - Updated member role to "owner" for user:', userId);
        } else if (membershipData[0]) {
          console.log('Debug - Member role already set to "owner" for user:', userId);
        } else {
          console.log('Debug - No membership found, Better Auth should have created it');
        }
      } catch (error) {
        console.error('Error verifying/updating member role:', error);
        // Don't fail the organization creation for this, but log it
      }

      return organizationData.id;
    } catch (error) {
      console.error('Error creating organization:', error);
      if (error instanceof HTTPException) {
        throw error;
      }
      throw new HTTPException(500, { message: "Failed to create organization" });
    }
  }

  /**
   * Create a membership for a user in an organization
   */
  static async createMembership(userId: string, organizationId: string, role: string = 'owner'): Promise<void> {
    try {
      await db.insert(member).values({
        id: randomUUID(),
        organizationId,
        userId,
        role,
        createdAt: new Date(),
      });

      console.log('Debug - Created membership for user as', role);
    } catch (error) {
      console.error('Error creating membership:', error);
      throw new HTTPException(500, { message: "Failed to create membership" });
    }
  }

  /**
   * Create a user profile
   */
  static async createUserProfile(userId: string, organizationId: string, orgData: OrganizationData): Promise<void> {
    try {
      const displayName = `${orgData.firstName} ${orgData.lastName}`;

      await db.insert(userProfiles).values({
        userId,
        organizationId,
        isActive: true,
        joinedAt: new Date().toISOString(),
        displayName,
        firstName: orgData.firstName,
        lastName: orgData.lastName,
        phone: orgData.phone,
        licenseNumber: orgData.licenseNumber,
        website: orgData.website,
        businessAddress: orgData.businessAddress,
        marketingConsent: orgData.marketingConsent || false,
        termsAccepted: orgData.termsAccepted,
        termsAcceptedAt: orgData.termsAccepted ? new Date().toISOString() : null,
      });

      console.log('Debug - Created user profile successfully');
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw new HTTPException(500, { message: "Failed to create user profile" });
    }
  }

  /**
   * Create user account with Better Auth only (no organization setup)
   */
  static async createUserAccount(data: SignUpData): Promise<{ authData: any; betterAuthResponse: Response }> {
    try {
      const response = await auth.api.signUpEmail({
        body: {
          name: data.name,
          email: data.email,
          password: data.password,
          image: data.image,
          callbackURL: data.callbackURL,
          rememberMe: data.rememberMe,
        },
        asResponse: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new HTTPException(response.status as any, {
          message: errorData.message || "Sign up failed"
        });
      }

      const authData = await response.json();

      // Debug: Log what Better Auth returns
      console.log('Better Auth sign-up response:', JSON.stringify(authData, null, 2));

      // Ensure user has the correct global platform role
      if (authData.user && (!authData.user.role || authData.user.role !== "user")) {
        try {
          // Update user role to "user" if not already set
          await db.update(user).set({ role: "user" }).where(eq(user.id, authData.user.id));
          authData.user.role = "user";
          console.log('Debug - Set user.role to "user" for user:', authData.user.id);
        } catch (error) {
          console.error('Error setting user role:', error);
          // Don't fail the signup for this, but log it
        }
      }

      // Create session object from Better Auth token response
      const sessionData: SessionData | null = authData.token ? {
        id: authData.token, // Using token as session ID for now
        userId: authData.user.id,
        token: authData.token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        createdAt: authData.user.createdAt,
        updatedAt: authData.user.updatedAt,
        ipAddress: null,
        userAgent: null,
        activeOrganizationId: null,
        impersonatedBy: null,
      } : null;

      // Return both the auth data with session and the Better Auth response (for cookies)
      return {
        authData: {
          ...authData,
          session: sessionData
        },
        betterAuthResponse: response
      };
    } catch (error: any) {
      if (error instanceof HTTPException) {
        throw error;
      }

      // Handle specific Better Auth errors
      if (error.message?.includes("User already exists")) {
        throw new HTTPException(409, { message: "User with this email already exists" });
      }

      console.error("User account creation error:", error);
      throw new HTTPException(500, { message: "Failed to create user account" });
    }
  }
}
