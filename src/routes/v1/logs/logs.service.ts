import { HTTPException } from "hono/http-exception";
import { eq, and, desc, count, lt, gte, lte, like } from "drizzle-orm";
import db from "@/db";
import { apiLogs } from "@/db/schema";
import type { AuthenticatedUser } from "@/middlewares/auth";

export interface LogFilters {
  page?: number;
  limit?: number;
  method?: string;
  status_code?: number;
  path?: string;
  user_id?: string;
  from_date?: string;
  to_date?: string;
}

export interface LogData {
  // Request information
  method: string;
  url: string;
  path: string;
  userAgent?: string;
  ipAddress?: string;
  
  // User context
  user?: AuthenticatedUser;
  
  // Request data
  headers?: Record<string, string>;
  queryParams?: Record<string, string>;
  requestBody?: any;
  
  // Response data
  statusCode?: number;
  responseBody?: any;
  responseHeaders?: Record<string, string>;
  
  // Timing
  startTime: string;
  endTime?: string;
  duration?: number;
  
  // Error information
  errorMessage?: string;
  errorStack?: string;
}

export class LogsService {
  /**
   * Log API request/response to database
   */
  static async addLog(data: LogData): Promise<void> {
    try {
      await db.insert(apiLogs).values({
        method: data.method,
        url: data.url,
        path: data.path,
        userAgent: data.userAgent,
        ipAddress: data.ipAddress,
        userId: data.user?.id,
        workspaceId: data.user?.workspace?.id,
        headers: data.headers,
        queryParams: data.queryParams,
        requestBody: data.requestBody,
        statusCode: data.statusCode,
        responseBody: data.responseBody,
        responseHeaders: data.responseHeaders,
        startTime: data.startTime,
        endTime: data.endTime,
        duration: data.duration,
        errorMessage: data.errorMessage,
        errorStack: data.errorStack,
      });
    } catch (error) {
      // Don't let logging errors break the API
      console.error('Failed to log API request:', error);
    }
  }

  /**
   * Get a single log entry by ID
   */
  static async getLogById(id: string, workspaceId?: string | undefined) {
    const conditions = [eq(apiLogs.id, id)];
    
    // Filter by workspace if provided (for multi-tenant access)
    if (workspaceId) {
      conditions.push(eq(apiLogs.workspaceId, workspaceId));
    }

    const log = await db
      .select()
      .from(apiLogs)
      .where(and(...conditions))
      .limit(1);

    if (log.length === 0) {
      throw new HTTPException(404, { message: "Log entry not found" });
    }

    return log[0];
  }

  /**
   * Get logs by specific path with optimized performance
   */
  static async getLogsByPath(
    path: string, 
    options: {
      page?: number;
      limit?: number;
      workspaceId?: string;
      method?: string;
      status_code?: number;
      from_date?: string;
      to_date?: string;
    } = {}
  ) {
    const { 
      page = 1, 
      limit = 20, 
      workspaceId, 
      method, 
      status_code, 
      from_date, 
      to_date 
    } = options;

    // Build optimized where conditions - path first for index usage
    const conditions = [eq(apiLogs.path, path)];

    // Add workspace filter early for multi-tenant optimization
    if (workspaceId) {
      conditions.push(eq(apiLogs.workspaceId, workspaceId));
    }

    // Add additional filters
    if (method) {
      conditions.push(eq(apiLogs.method, method.toUpperCase()));
    }

    if (status_code) {
      conditions.push(eq(apiLogs.statusCode, status_code));
    }

    if (from_date) {
      conditions.push(gte(apiLogs.createdAt, from_date));
    }

    if (to_date) {
      conditions.push(lte(apiLogs.createdAt, to_date));
    }

    const whereClause = and(...conditions);

    // Get total count for pagination (optimized with same where clause)
    const [{ total }] = await db
      .select({ total: count() })
      .from(apiLogs)
      .where(whereClause);

    // Apply pagination and get results with optimized ordering
    const offset = (page - 1) * limit;
    const results = await db
      .select({
        id: apiLogs.id,
        method: apiLogs.method,
        url: apiLogs.url,
        path: apiLogs.path,
        statusCode: apiLogs.statusCode,
        duration: apiLogs.duration,
        userAgent: apiLogs.userAgent,
        ipAddress: apiLogs.ipAddress,
        userId: apiLogs.userId,
        workspaceId: apiLogs.workspaceId,
        requestHeaders: apiLogs.headers,
        requestBody: apiLogs.requestBody,
        responseBody: apiLogs.responseBody,
        responseHeaders: apiLogs.responseHeaders,
        errorMessage: apiLogs.errorMessage,
        createdAt: apiLogs.createdAt,
      })
      .from(apiLogs)
      .where(whereClause)
      .orderBy(desc(apiLogs.createdAt)) // Use indexed column for ordering
      .limit(limit)
      .offset(offset);

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
      summary: {
        path,
        totalRequests: total,
        ...(method && { method }),
        ...(status_code && { statusCode: status_code }),
      },
    };
  }

  /**
   * Get logs statistics
   */
  static async getLogsStats(workspaceId?: string) {
    const conditions = workspaceId ? [eq(apiLogs.workspaceId, workspaceId)] : [];
    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const stats = await db
      .select({
        total: count(),
        errors: count(apiLogs.statusCode),
        avgDuration: count(apiLogs.duration),
      })
      .from(apiLogs)
      .where(whereClause);

    return stats[0];
  }
} 