import type { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import type { AuthenticatedUser } from "@/middlewares/auth";
import { createErrorResponse } from "@/middlewares/validation";

/**
 * Authentication and authorization utility functions
 */

/**
 * Safely get authenticated user from context
 */
export function getAuthenticatedUser(c: Context): AuthenticatedUser {
  const user = c.get("user") as AuthenticatedUser;
  if (!user) {
    throw new HTTPException(401, {
      message: JSON.stringify(
        createErrorResponse(
          "AUTHENTICATION_REQUIRED",
          "Authentication required to access this resource",
          c.req.path
        )
      ),
    });
  }
  return user;
}

/**
 * Get user workspace with validation
 * Note: In this codebase, "workspace" and "organization" are used interchangeably
 */
export function getUserWorkspace(c: Context) {
  const user = getAuthenticatedUser(c);
  if (!user.organization) {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "WORKSPACE_REQUIRED",
          "User must belong to a workspace to access this resource",
          c.req.path
        )
      ),
    });
  }
  return user.organization;
}

/**
 * Get user profile with validation
 */
export function getUserProfile(c: Context) {
  const user = getAuthenticatedUser(c);
  if (!user.profile) {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "PROFILE_REQUIRED",
          "User profile is required to access this resource",
          c.req.path
        )
      ),
    });
  }
  return user.profile;
}

/**
 * Check if user has required role
 */
export function requireUserRole(c: Context, ...allowedRoles: string[]) {
  const profile = getUserProfile(c);
  if (!allowedRoles.includes(profile.role)) {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "INSUFFICIENT_PERMISSIONS",
          `Access denied. Required roles: ${allowedRoles.join(", ")}`,
          c.req.path
        )
      ),
    });
  }
}

/**
 * Check if user can access specific workspace
 */
export function requireWorkspaceAccess(c: Context, workspaceId: string) {
  const workspace = getUserWorkspace(c);
  if (workspace.id !== workspaceId) {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "WORKSPACE_ACCESS_DENIED",
          "Access denied to this workspace",
          c.req.path
        )
      ),
    });
  }
}

/**
 * Check if workspace is active
 */
export function requireActiveWorkspace(c: Context) {
  const workspace = getUserWorkspace(c);
  if (workspace.status === "suspended") {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "WORKSPACE_SUSPENDED",
          "Workspace is suspended",
          c.req.path
        )
      ),
    });
  }
  if (workspace.status === "inactive") {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "WORKSPACE_INACTIVE",
          "Workspace is inactive",
          c.req.path
        )
      ),
    });
  }
}

/**
 * Check if user has admin privileges
 */
export function requireAdminRole(c: Context) {
  requireUserRole(c, "owner", "admin");
}

/**
 * Check if user can manage team members
 */
export function requireTeamManagementRole(c: Context) {
  requireUserRole(c, "owner", "admin", "manager");
}

/**
 * Validate resource ownership or admin access
 */
export function requireResourceAccess(c: Context, resourceUserId: string) {
  const user = getAuthenticatedUser(c);
  const profile = getUserProfile(c);

  // Allow access if user owns the resource or is admin
  const isOwner = user.id === resourceUserId;
  const isAdmin = ["owner", "admin"].includes(profile.role);

  if (!isOwner && !isAdmin) {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "RESOURCE_ACCESS_DENIED",
          "Access denied to this resource",
          c.req.path
        )
      ),
    });
  }
}

/**
 * Get validated request data
 */
export function getValidatedBody<T>(c: Context): T {
  const body = c.get("validatedBody");
  if (!body) {
    throw new HTTPException(400, {
      message: JSON.stringify(
        createErrorResponse(
          "VALIDATION_MISSING",
          "Request body validation not performed",
          c.req.path
        )
      ),
    });
  }
  return body as T;
}

export function getValidatedQuery<T>(c: Context): T {
  const query = c.get("validatedQuery");
  if (!query) {
    throw new HTTPException(400, {
      message: JSON.stringify(
        createErrorResponse(
          "VALIDATION_MISSING",
          "Query parameters validation not performed",
          c.req.path
        )
      ),
    });
  }
  return query as T;
}

export function getValidatedParams<T>(c: Context): T {
  const params = c.get("validatedParams");
  if (!params) {
    throw new HTTPException(400, {
      message: JSON.stringify(
        createErrorResponse(
          "VALIDATION_MISSING",
          "Path parameters validation not performed",
          c.req.path
        )
      ),
    });
  }
  return params as T;
}

/**
 * Role hierarchy for permission checking
 */
export const ROLE_HIERARCHY = {
  owner: 4,
  admin: 3,
  manager: 2,
  member: 1,
  viewer: 0,
} as const;

/**
 * Check if user role has sufficient permissions
 */
export function hasMinimumRole(
  userRole: string,
  requiredRole: keyof typeof ROLE_HIERARCHY
): boolean {
  const userLevel =
    ROLE_HIERARCHY[userRole as keyof typeof ROLE_HIERARCHY] ?? -1;
  const requiredLevel = ROLE_HIERARCHY[requiredRole];
  return userLevel >= requiredLevel;
}

/**
 * Require minimum role level
 */
export function requireMinimumRole(
  c: Context,
  requiredRole: keyof typeof ROLE_HIERARCHY
) {
  const profile = getUserProfile(c);
  if (!hasMinimumRole(profile.role, requiredRole)) {
    throw new HTTPException(403, {
      message: JSON.stringify(
        createErrorResponse(
          "INSUFFICIENT_ROLE",
          `Minimum role required: ${requiredRole}`,
          c.req.path
        )
      ),
    });
  }
}
